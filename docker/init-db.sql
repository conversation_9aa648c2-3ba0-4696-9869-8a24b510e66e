-- Initialize MTBRMG ERP Database
-- This script runs when the PostgreSQL container starts for the first time

-- Create the main database (already created by POSTGRES_DB env var)
-- CREATE DATABASE mtbrmg_erp;

-- Create additional databases for testing if needed
-- CREATE DATABASE mtbrmg_erp_test;

-- Grant all privileges to the postgres user (already done by default)
-- GRANT ALL PRIVILEGES ON DATABASE mtbrmg_erp TO postgres;

-- You can add any additional database initialization here
-- For example, creating extensions:
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Log that initialization is complete
SELECT 'MTBRMG ERP Database initialized successfully' AS status;
