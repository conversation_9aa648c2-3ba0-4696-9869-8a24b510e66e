# Django Backend Environment Variables for Docker
DEBUG=True
SECRET_KEY=django-insecure-docker-development-key-change-in-production

# Database Configuration
DB_NAME=mtbrmg_erp
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=postgres
DB_PORT=5432
USE_SQLITE=False

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# CORS Configuration
ALLOWED_HOSTS=localhost,127.0.0.1,backend,frontend
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://frontend:3001

# Session Configuration
SESSION_COOKIE_AGE=31536000
SESSION_EXPIRE_AT_BROWSER_CLOSE=False
SESSION_SAVE_EVERY_REQUEST=False
SESSION_COOKIE_SECURE=False

# JWT Configuration
JWT_ACCESS_TOKEN_HOURS=24
JWT_REFRESH_TOKEN_DAYS=30

# Email Configuration (optional for development)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# AWS Configuration (optional for development)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=us-east-1
